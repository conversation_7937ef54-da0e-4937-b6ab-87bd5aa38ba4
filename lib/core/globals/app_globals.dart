/// Global application state variables
class AppGlobals {
  /// Currently emulated user ID (null when not emulating)
  static String? currentEmulatedUserId;

  /// Set the emulated user ID
  static void setEmulatedUserId(String userId) {
    currentEmulatedUserId = userId;
  }

  /// Get the current emulated user ID
  static String? getEmulatedUserId() {
    return currentEmulatedUserId;
  }

  /// Clear the emulated user ID (return to original user)
  static void clearEmulatedUserId() {
    currentEmulatedUserId = null;
  }

  /// Check if currently emulating a user
  static bool isEmulating() {
    return currentEmulatedUserId != null;
  }
}